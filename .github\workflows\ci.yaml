name: For each commit and PR
on:
  push:
  pull_request:
jobs:
  validation:
    runs-on: ubuntu-latest
    env:
      CGO_ENABLED: 0
    steps:
    - name: Init
      run: sudo apt-get update && sudo apt-get install -y build-essential golint
    - name: Checkout code
      uses: actions/checkout@v2
    - name: Install Go
      uses: actions/setup-go@v2
      with:
        go-version: '1.18'
    - name: Install golangci-lint
      run: curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.45.2
    - name: checks
      run: make check
    - name: test docker build
      run: make dockerx86Action
    - name: Manifest generate
      run: ./testing/testing.sh
    - name: e2e tests
      run: DOCKERTAG=action make e2e-tests
