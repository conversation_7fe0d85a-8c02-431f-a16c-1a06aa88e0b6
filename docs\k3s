#!/bin/bash

echo "apiVersion: apps/v1
kind: DaemonSet
metadata:
  creationTimestamp: null
  name: kube-vip-ds
  namespace: kube-system
spec:
  selector:
    matchLabels:
      name: kube-vip-ds
  template:
    metadata:
      creationTimestamp: null
      labels:
        name: kube-vip-ds
    spec:
      containers:
      - args:
        - manager
        env:
        - name: vip_arp
          value: \"true\"
        - name: vip_interface
          value: $vipInterface
        - name: port
          value: \"6443\"
        - name: vip_cidr
          value: \"32\"
        - name: cp_enable
          value: \"true\"
        - name: cp_namespace
          value: kube-system
        - name: svc_enable
          value: \"false\"
        - name: vip_address
          value: $vipAddress
        image: ghcr.io/kube-vip/kube-vip:v0.3.7
        imagePullPolicy: Always
        name: kube-vip
        resources: {}
        securityContext:
          capabilities:
            add:
            - NET_ADMIN
            - NET_RAW
            - SYS_TIME
      hostNetwork: true
      nodeSelector:
        node-role.kubernetes.io/master: \"true\"
      serviceAccountName: kube-vip
      tolerations:
      - effect: NoSchedule
        key: node-role.kubernetes.io/master
  updateStrategy: {}
status:
  currentNumberScheduled: 0
  desiredNumberScheduled: 0
  numberMisscheduled: 0
  numberReady: 0"