name: Build and publish main image regularly

on:
  schedule:
    - cron: '25 0 * * *'

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Login to Github Packages
        uses: docker/login-action@v1
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}   
      - name: Build and push main branch
        id: docker_build
        uses: docker/build-push-action@v2
        with:
          context: .
          platforms: linux/amd64,linux/arm/v7,linux/arm64,linux/ppc64le,linux/s390x
          push: ${{ github.event_name != 'pull_request' }}
          tags: >-
            plndr/kube-vip:${{ github.ref_name }},
            ghcr.io/kube-vip/kube-vip:${{ github.ref_name }}
      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}