apiVersion: v1
kind: ServiceAccount
metadata:
  name: kube-vip
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    rbac.authorization.kubernetes.io/autoupdate: "true"
  name: system:kube-vip-role
rules:
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["get", "create", "update", "list", "put"]
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["list","get","watch", "update"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: system:kube-vip-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:kube-vip-role
subjects:
- kind: ServiceAccount
  name: kube-vip
  namespace: kube-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: kube-vip-workers
  name: kube-vip-workers
  namespace: kube-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: kube-vip-workers
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: kube-vip-workers
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
                - key: "app"
                  operator: In
                  values:
                  - kube-vip-workers
            topologyKey: "kubernetes.io/hostname"
      containers:
      - image: ghcr.io/kube-vip/kube-vip:0.3.7
        imagePullPolicy: Always
        name: kube-vip
        command:
        - /kube-vip
        - service
        env:
          - name: vip_interface
            value: "ens160"
          - name: vip_arp
            value: "true"
          - name: vip_loglevel
            value: "5"
        resources: {}
        securityContext:
          capabilities:
            add:
            - NET_ADMIN
      hostNetwork: true
      serviceAccountName: kube-vip
status: {}
